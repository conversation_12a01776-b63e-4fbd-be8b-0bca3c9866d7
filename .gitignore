# Django
logs/django.log
*.log
*.pot
*.pyc
__pycache__/
local_settings.py
db.sqlite3
db.sqlite3-journal

# Media files (user uploads) - DO NOT COMMIT LARGE FILES
media/
temp_uploads/
media_files/

# Large media file extensions
*.mp4
*.mkv
*.avi
*.mov
*.flv
*.webm
*.wav
*.mp3
*.m4a
*.aac

# Generated subtitle files
*.vtt
*.srt
*.txt

# Large archive files
*.zip
*.tar.gz
*.rar
*.7z

# Environment variables
.env
.env.local
.env.development.local
.env.test.local
.env.production.local

# Python
*.py[cod]
*$py.class
*.so
.Python
build/
develop-eggs/
dist/
downloads/
eggs/
.eggs/
lib/
lib64/
parts/
sdist/
var/
wheels/
*.egg-info/
.installed.cfg
*.egg
MANIFEST

# Virtual environments
venv/
env/
ENV/
env.bak/
venv.bak/

# IDE
.vscode/
.idea/
*.swp
*.swo
*~

# OS
.DS_Store
.DS_Store?
._*
.Spotlight-V100
.Trashes
ehthumbs.db
Thumbs.db

# Note: Frontend-specific ignores are in frontend/.gitignore